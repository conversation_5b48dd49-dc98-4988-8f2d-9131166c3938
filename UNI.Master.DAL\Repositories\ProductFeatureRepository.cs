﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Master.DAL.Interfaces;
using UNI.Master.Model.UniMaster;
using UNI.Model;

namespace UNI.Master.DAL.Repositories
{
    public class ProductFeatureRepository : UniBaseRepository, IProductFeatureRepository
    {
        public ProductFeatureRepository(IUniCommonBaseRepository commonInfo) : base(commonInfo)
        {
        }

        public async Task<CommonListPage> GetProductFeaturePage(FilterProdPackage flt)
        {
            const string storedProcedure = "sp_product_features_get";
            return await base.GetPageAsync(storedProcedure, flt, param =>
            {
                param.Add("@product_id", flt.productId);
                return param;
            });
        }

        public async Task<productFeatureInfo> GetProductFeatureInfo(Guid? id)
        {
            const string storedProcedure = "sp_product_features_fields";
            return await base.GetFieldsAsync<productFeatureInfo>(storedProcedure, new { id });
        }

        public async Task<BaseValidate> SetProductFeatureInfo(productFeatureInfo query)
        {
            const string storedProcedure = "sp_product_features_set";
            return await base.SetInfoAsync<BaseValidate>(storedProcedure, query, new { query.id });
        }

        public async Task<BaseValidate> DelProductFeatureInfo(Guid id)
        {
            const string storedProcedure = "sp_product_features_del";
            return await base.DeleteAsync(storedProcedure, new { id });
        }

        public async Task<List<CommonValue>> GetProductFeatureList(string filter, string product_id)
        {
            const string storedProcedure = "sp_product_feature_list_get";
            return await base.GetListAsync<CommonValue>(storedProcedure, new { filter, product_id });
        }
    }
}
