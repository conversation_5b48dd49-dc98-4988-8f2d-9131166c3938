﻿using UNI.Model;
using UNI.Master.Model.UniMaster;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UNI.Master.BLL.Interfaces;
using UNI.Master.DAL.Interfaces;

namespace UNI.Master.BLL.BusinessService
{
    public class ProductPackageService : IProductPackageService
    {
        private readonly IProductPackageRepository _productPackageRepository;
        public ProductPackageService(IProductPackageRepository productPackageRepository)
        {
            if (productPackageRepository != null)
                _productPackageRepository = productPackageRepository;
        }

        public Task<BaseValidate> DelProductPackageInfo(Guid id)
        {
            return _productPackageRepository.DelProductPackageInfo(id);
        }

        public Task<productPackageInfo> GetProductPackageInfo(Guid? id)
        {
            return _productPackageRepository.GetProductPackageInfo(id);
        }

        public Task<CommonListPage> GetProductPackagePage(FilterProdPackage query)
        {
            return _productPackageRepository.GetProductPackagePage(query);
        }

        public Task<BaseValidate> SetProductPackageInfo(productPackageInfo prod)
        {
            return _productPackageRepository.SetProductPackageInfo(prod);
        }
        public Task<List<CommonValue>> GetProductPackageList(string filter)
        {
            return _productPackageRepository.GetProductPackageList(filter);
        }
        public Task<CommonViewInfo> GetProductPackageDetailInfo(Guid? id, Guid? package_id)
        {
            return _productPackageRepository.GetProductPackageDetailInfo(id, package_id);
        }
        public Task<BaseValidate> SetProductPackageDetailInfo(CommonViewInfo query)
        {
            return _productPackageRepository.SetProductPackageDetailInfo(query);
        }


        public Task<ProductPackageDetailDescriptionInfo> GetProductPackageDetailDescriptionInfo(ProductPackageDetailDescriptionGet data)
        {
            return _productPackageRepository.GetProductPackageDetailDescriptionInfo(data);
        }
        public Task<BaseValidate> SetProductPackageDetailDescriptionInfo(ProductPackageDetailDescriptionInfo query)
        {
            return _productPackageRepository.SetProductPackageDetailDescriptionInfo(query);
        }
        public Task<BaseValidate> DelProductPackageDetailDescriptionInfo(Guid id)
        {
            return _productPackageRepository.DelProductPackageDetailDescriptionInfo(id);
        }
        public Task<CommonListPage> GetProductPackageDetailDescriptionList(FilterProductPackageDetailDescription flt)
        {
            return _productPackageRepository.GetProductPackageDetailDescriptionList(flt);
        }
    }
}
